# AI-Speak 任务接口测试脚本

## 概述

`test-tasks.sh` 是一个整合了交互式测试和自动化测试功能的综合测试脚本，用于测试 AI-Speak 任务管理系统的各种 API 接口。

## 功能特性

### 🔄 两种运行模式

1. **交互式菜单模式**（默认）
   - 提供19个测试选项的菜单界面
   - 支持用户输入参数进行个性化测试
   - 适合开发调试和手动测试

2. **自动化测试模式**
   - 按顺序自动执行端到端测试流程
   - 无需用户交互，适合CI/CD集成
   - 提供详细的测试结果摘要

### 📋 测试覆盖范围

#### 基本功能测试
- ✅ 获取所有任务
- ✅ 按ID查询任务
- ✅ 按状态过滤任务
- ✅ 创建各种类型任务（听写、跟读、拼写、综合测验）
- ✅ 更新任务
- ✅ 删除任务
- ✅ 提交任务
- ✅ 评分提交

#### 验证测试
- ✅ 缺少必填字段验证
- ✅ 无效字段值验证
- ✅ 任务类型和内容类型匹配验证
- ✅ 已发布任务截止时间验证

#### 任务类型支持
- 📝 听写任务 (dictation)
- 🗣️ 句子跟读任务 (sentence_repeat)
- ✏️ 拼写任务 (spelling)
- 📊 综合测验任务 (quiz)
- 🤖 自动生成模式

## 使用方法

### 前置条件

1. 确保已安装 `jq` 工具：
   ```bash
   brew install jq  # macOS
   # 或
   sudo apt-get install jq  # Ubuntu/Debian
   ```

2. 确保 AI-Speak 服务器正在运行（默认端口：8080）

### 运行方式

#### 1. 交互式菜单模式
```bash
./test-tasks.sh
```

运行后会显示菜单，选择对应的数字即可执行相应测试：
```
请选择要运行的测试场景:
1) 基本听写任务
2) 句子跟读任务
3) 拼写任务
4) 综合测验任务
...
```

#### 2. 自动化测试模式
```bash
./test-tasks.sh auto
```

自动执行完整的端到端测试流程：
1. 获取所有任务
2. 创建测试班级
3. 创建基本听写任务
4. 验证测试（缺少标题）
5. 验证测试（类型不匹配）
6. 获取任务详情
7. 提交任务
8. 评分提交

## 配置说明

### API 配置
脚本默认连接到本地服务器：
```bash
BASE_URL="http://localhost:8080/api/v1"
```

如需修改服务器地址，请编辑脚本中的 `BASE_URL` 变量。

### 测试数据
- 默认教师ID：`teacher001`
- 自动创建的班级ID：`test_class_001`
- 测试用词汇ID：`[1, 2, 3]`
- 测试用句子ID：`[1, 2, 3]`

## 输出说明

### 成功示例
```
✓ 创建班级 成功! 状态码: 201
✓ 基本听写任务 成功! 状态码: 201
任务ID: 123
```

### 失败示例
```
✗ 验证测试 - 缺少标题 失败! 状态码: 500 (期望: 400)
```

### 自动化测试摘要
```
=== 测试完成 ===
总测试数: 8
成功测试数: 7
失败测试数: 1
✗ 部分测试失败
```

## 整合说明

此脚本整合了原来的 `test-tasks.sh` 和 `simple-test.sh` 的功能：

- **保留了** `test-tasks.sh` 的交互式菜单和丰富的测试场景
- **整合了** `simple-test.sh` 的自动化测试流程和依赖关系处理
- **移除了** 冗余代码，统一了响应处理逻辑
- **增强了** 错误处理和状态码验证

## 故障排除

### 常见问题

1. **jq 命令未找到**
   ```
   错误: 需要安装jq来格式化JSON输出
   请运行: brew install jq
   ```

2. **连接服务器失败**
   - 检查服务器是否正在运行
   - 确认端口号是否正确（默认8080）

3. **权限错误**
   ```bash
   chmod +x test-tasks.sh
   ```

### 调试模式

在自动化模式下，脚本会隐藏详细的响应内容以保持输出简洁。如需查看详细信息，请使用交互式模式进行单个测试。
